import ProFormImg from '@/components/ProFormItem/ProFormImg';
import { ModalForm, ProFormText } from '@ant-design/pro-components';
import { Form } from 'antd';
import React, { useEffect } from 'react';

type EditModalProps = {
  open: boolean;
  info?: API.Banner;
  onSave: (info: API.Banner) => Promise<void>;
  onClose: () => void;
};

const EditModal: React.FC<EditModalProps> = ({
  open,
  info,
  onSave,
  onClose,
}) => {
  const [form] = Form.useForm();

  // 当模态框打开时重置表单
  useEffect(() => {
    if (open) {
      if (info) {
        // 编辑模式：设置表单值
        form.setFieldsValue(info);
      } else {
        // 新增模式：重置表单
        form.resetFields();
      }
    }
  }, [open, info, form]);

  return (
    <ModalForm<API.Banner>
      title={info ? '编辑图片' : '添加图片'}
      autoFocusFirstInput
      modalProps={{
        destroyOnClose: true,
        onCancel: onClose,
      }}
      open={open}
      layout="horizontal"
      grid
      labelCol={{ flex: '7em' }}
      onFinish={onSave}
      form={form}
    >
      <ProFormText name="id" label="ID" hidden />
      <ProFormImg
        name="imageURL"
        label="图片"
        dir="banner"
        maxSize={{ size: 1024 * 1024 * 2, message: '图片大小不能超过2M' }}
        rules={[{ required: true, message: '请输入名称！' }]}
      />
      <ProFormText name="jumpLink" label="跳转链接" colProps={{ span: 18 }} />
      <ProFormText name="sort" label="排序" colProps={{ span: 6 }} />
    </ModalForm>
  );
};

export default EditModal;
