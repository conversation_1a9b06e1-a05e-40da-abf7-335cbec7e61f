# 批量修复编辑模态框脚本指南

## 需要修复的文件列表

以下是需要修复的编辑模态框文件：

### 1. 简单修复（无复杂状态）

这些文件只需要添加基本的表单重置逻辑：

- `src/pages/settings/Roles/EditModal.tsx`
- `src/pages/settings/Users/<USER>
- `src/pages/basicData/Area/EditModal.tsx`
- `src/pages/basicData/Dictionarie/EditModal.tsx`
- `src/pages/Vehicle/EditModal.tsx`

### 2. 复杂修复（有状态管理）

这些文件需要额外处理组件内部状态：

- `src/pages/serviceManager/Service/EditModal.tsx` (有富文本编辑器)

## 修复模板

### 简单修复模板

```typescript
// 1. 添加导入
import { Form } from 'antd';
import React, { useEffect } from 'react';

// 2. 在组件内添加
const [form] = Form.useForm();

// 3. 添加useEffect
useEffect(() => {
  if (open) {
    if (info) {
      // 编辑模式：设置表单值
      form.setFieldsValue(info);
    } else {
      // 新增模式：重置表单
      form.resetFields();
      // 如果有默认值，在这里设置
      // form.setFieldsValue({ defaultField: 'defaultValue' });
    }
  }
}, [open, info, form]);

// 4. 在ModalForm中
// 移除 initialValues={info}
// 添加 form={form}
```

### 复杂修复模板（有状态管理）

```typescript
// 除了上述基本修复外，还需要：

// 1. 在useEffect中同步更新组件状态
useEffect(() => {
  if (open) {
    if (info) {
      // 编辑模式
      form.setFieldsValue(info);
      // 同步更新其他状态
      setSomeState(info.someField);
      setAnotherState(info.anotherField);
    } else {
      // 新增模式
      form.resetFields();
      // 重置其他状态到默认值
      setSomeState(defaultValue);
      setAnotherState(defaultValue);
    }
  }
}, [open, info, form]);
```

## 修复步骤

### 步骤1：备份文件
在修复前，建议先提交当前代码或创建备份。

### 步骤2：逐个修复
按照以下顺序修复：

1. **settings/Roles/EditModal.tsx**
2. **settings/Users/<USER>
3. **basicData/Area/EditModal.tsx**
4. **basicData/Dictionarie/EditModal.tsx**
5. **Vehicle/EditModal.tsx**
6. **serviceManager/Service/EditModal.tsx** (最复杂)

### 步骤3：测试
每修复一个文件，都要测试：
1. 新增功能是否正常
2. 编辑功能是否正常
3. 连续编辑不同记录是否正确切换数据

## 注意事项

1. **Form导入**：确保从'antd'导入Form
2. **useEffect导入**：确保从'react'导入useEffect
3. **依赖数组**：useEffect的依赖数组必须包含[open, info, form]
4. **默认值处理**：注意处理有默认值的字段
5. **状态同步**：有内部状态的组件要同步更新状态

## 验证清单

修复完成后，检查以下项目：

- [ ] 导入语句正确
- [ ] useEffect逻辑完整
- [ ] 移除了initialValues属性
- [ ] 添加了form属性
- [ ] 测试新增功能
- [ ] 测试编辑功能
- [ ] 测试连续编辑不同记录

## 已修复文件

- ✅ `src/pages/Miniprogram/Activity/EditModal.tsx`
- ✅ `src/pages/Miniprogram/Banner/EditModal.tsx`

## 示例对比

### 修复前
```typescript
const EditModal: React.FC<EditModalProps> = ({ open, info, onSave, onClose }) => {
  return (
    <ModalForm
      open={open}
      onFinish={onSave}
      initialValues={info}  // 问题所在
    >
      {/* 表单项 */}
    </ModalForm>
  );
};
```

### 修复后
```typescript
const EditModal: React.FC<EditModalProps> = ({ open, info, onSave, onClose }) => {
  const [form] = Form.useForm();

  useEffect(() => {
    if (open) {
      if (info) {
        form.setFieldsValue(info);
      } else {
        form.resetFields();
      }
    }
  }, [open, info, form]);

  return (
    <ModalForm
      open={open}
      onFinish={onSave}
      form={form}  // 使用form实例
    >
      {/* 表单项 */}
    </ModalForm>
  );
};
```
