import ProFormImg from '@/components/ProFormItem/ProFormImg';
import WangEditor, { WangEditorRef } from '@/components/WangEditor';
import {
  ModalForm,
  ProFormRadio,
  ProFormText,
} from '@ant-design/pro-components';
import { Form } from 'antd';
import React, { useRef, useState } from 'react';

type EditModalProps = {
  open: boolean;
  info?: API.Activity;
  onSave: (info: API.Activity) => Promise<void>;
  onClose: () => void;
};

const EditModal: React.FC<EditModalProps> = ({
  open,
  info,
  onSave,
  onClose,
}) => {
  const [form] = Form.useForm();
  const editorRef = useRef<WangEditorRef>(null);
  const [contentType, setContentType] = useState<'content' | 'url'>('content');

  // 当info变化时更新contentType
  React.useEffect(() => {
    if (info?.contentType) {
      setContentType(info.contentType);
    } else {
      setContentType('content');
    }
  }, [info]);

  const handleFinish = async (values: any) => {
    let activityData = { ...values };

    if (values.contentType === 'content') {
      const content = editorRef.current?.getValue() || '';
      activityData = {
        ...values,
        content,
        url: undefined, // 清空url字段
      };
    } else {
      activityData = {
        ...values,
        content: undefined, // 清空content字段
      };
    }

    try {
      await onSave(activityData);
      // 只有在富文本模式下才清理媒体文件
      if (values.contentType === 'content') {
        editorRef.current?.clearDeletedMedia();
      }
    } catch (error) {
      // 如果保存失败，不清理媒体文件
      console.error('保存失败:', error);
    }
  };

  return (
    <ModalForm<API.Activity>
      title={info ? '编辑活动' : '新增活动'}
      autoFocusFirstInput
      modalProps={{
        destroyOnClose: true,
        onCancel: onClose,
        width: 800,
      }}
      open={open}
      layout="horizontal"
      grid
      labelCol={{ flex: '7em' }}
      onFinish={handleFinish}
      initialValues={info}
      form={form}
    >
      <ProFormText name="id" label="ID" hidden />
      
      <ProFormText
        name="title"
        label="活动标题"
        colProps={{ span: 24 }}
        rules={[
          { required: true, message: '请输入活动标题！' },
          { max: 100, message: '标题最多100个字符！' },
        ]}
      />

      <ProFormText name="target" hidden initialValue="用户端" />



      <ProFormRadio.Group
        name="contentType"
        label="内容类型"
        colProps={{ span: 24 }}
        options={[
          { label: '富文本内容', value: 'content' },
          { label: '链接地址', value: 'url' },
        ]}
        initialValue="content"
        fieldProps={{
          onChange: (e) => {
            setContentType(e.target.value);
            // 清空相关字段
            if (e.target.value === 'content') {
              form.setFieldValue('url', undefined);
            } else {
              form.setFieldValue('content', undefined);
            }
          },
        }}
        rules={[{ required: true, message: '请选择内容类型！' }]}
      />

      <ProFormImg
        name="coverImage"
        label="封面图片"
        dir="activity"
        colProps={{ span: 24 }}
        maxSize={{ size: 1024 * 1024 * 2, message: '图片大小不能超过2M' }}
      />

      {contentType === 'content' && (
        <Form.Item
          label="活动内容"
          name="content"
          rules={[{ required: contentType === 'content', message: '请输入活动内容！' }]}
          style={{ gridColumn: '1 / -1' }}
        >
          <WangEditor
            ref={editorRef}
            value={form.getFieldValue('content') || info?.content || ''}
            onChange={(value) => {
              form.setFieldValue('content', value);
            }}
            style={{ minHeight: '300px' }}
          />
        </Form.Item>
      )}

      {contentType === 'url' && (
        <ProFormText
          name="url"
          label="活动链接"
          colProps={{ span: 24 }}
          placeholder="请输入活动页面的完整链接地址，如：https://example.com/activity"
          rules={[
            { required: contentType === 'url', message: '请输入活动链接！' },
            { type: 'url', message: '请输入有效的链接地址！' },
          ]}
        />
      )}
    </ModalForm>
  );
};

export default EditModal;
