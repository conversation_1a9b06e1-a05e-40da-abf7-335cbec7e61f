import { request } from '@umijs/max';

/** 查询活动列表  GET /admin/activities */
export async function index(params: Record<string, any>) {
  return request<API.ResType<{ total?: number; list?: API.Activity[] }>>(
    '/admin/activities',
    {
      method: 'GET',
      params,
    },
  );
}

/** 查询活动详情  GET /admin/activities/:id */
export async function show(id: number) {
  return request<API.ResType<API.Activity>>(`/admin/activities/${id}`, {
    method: 'GET',
  });
}

/** 创建活动  POST /admin/activities */
export async function create(body: Omit<API.Activity, 'id' | 'createdAt' | 'updatedAt' | 'publishedAt' | 'isPublished'>) {
  return request<API.ResType<API.Activity>>('/admin/activities', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
  });
}

/** 更新活动  PUT /admin/activities/:id */
export async function update(id: number, body: Partial<Omit<API.Activity, 'id' | 'createdAt' | 'updatedAt' | 'publishedAt' | 'isPublished'>>) {
  return request<API.ResType<unknown>>(`/admin/activities/${id}`, {
    method: 'PUT',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
  });
}

/** 删除活动  DELETE /admin/activities/:id */
export async function remove(id: number) {
  return request<API.ResType<unknown>>(`/admin/activities/${id}`, {
    method: 'DELETE',
  });
}



/** 发布活动  POST /admin/activities/:id/publish */
export async function publish(id: number) {
  return request<API.ResType<unknown>>(`/admin/activities/${id}/publish`, {
    method: 'POST',
  });
}

/** 取消发布活动  POST /admin/activities/:id/unpublish */
export async function unpublish(id: number) {
  return request<API.ResType<unknown>>(`/admin/activities/${id}/unpublish`, {
    method: 'POST',
  });
}



/** 查询当前发布的活动  GET /admin/activities/published/current */
export async function getCurrentPublished(target: string = '用户端') {
  return request<API.ResType<API.Activity>>('/admin/activities/published/current', {
    method: 'GET',
    params: { target },
  });
}
