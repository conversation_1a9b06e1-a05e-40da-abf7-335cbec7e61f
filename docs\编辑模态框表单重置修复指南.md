# 编辑模态框表单重置修复指南

## 问题描述

项目中存在一个系统性问题：在列表中点击编辑，弹出编辑窗，然后关闭再编辑另一条记录时，回填的信息还是前一个记录的数据，需要关闭并重新打开一次编辑，回填的才是新选中的记录信息。

## 问题原因

使用ModalForm组件时，如果只依赖`initialValues`属性，在模态框关闭后再次打开编辑不同记录时，表单不会自动重置，导致显示的还是上一条记录的数据。

## 解决方案

### 1. 修复方法

在编辑模态框组件中添加`useEffect`监听`open`和`info`的变化，主动管理表单状态：

```typescript
import React, { useEffect } from 'react';
import { Form } from 'antd';

const EditModal: React.FC<EditModalProps> = ({
  open,
  info,
  onSave,
  onClose,
}) => {
  const [form] = Form.useForm();

  // 当模态框打开时重置表单和状态
  useEffect(() => {
    if (open) {
      if (info) {
        // 编辑模式：设置表单值和状态
        form.setFieldsValue(info);
        // 如果有其他状态需要同步更新，在这里处理
      } else {
        // 新增模式：重置表单和状态
        form.resetFields();
        // 设置默认值
        form.setFieldsValue({
          // 默认值
        });
      }
    }
  }, [open, info, form]);

  return (
    <ModalForm
      // 移除 initialValues 属性
      form={form}
      // 其他属性...
    >
      {/* 表单项 */}
    </ModalForm>
  );
};
```

### 2. 关键要点

1. **添加useEffect监听**：监听`open`和`info`的变化
2. **移除initialValues**：不再使用`initialValues={info}`
3. **主动管理表单状态**：使用`form.setFieldsValue()`和`form.resetFields()`
4. **处理组件状态**：如果组件内有其他状态（如contentType等），也需要在useEffect中同步更新

### 3. 需要修复的文件列表

根据代码分析，以下文件可能需要修复：

- `src/pages/Miniprogram/Activity/EditModal.tsx` ✅ 已修复
- `src/pages/Miniprogram/Banner/EditModal.tsx`
- `src/pages/settings/Roles/EditModal.tsx`
- `src/pages/settings/Users/<USER>
- `src/pages/basicData/Area/EditModal.tsx`
- `src/pages/basicData/Dictionarie/EditModal.tsx`
- `src/pages/Vehicle/EditModal.tsx`
- `src/pages/serviceManager/Service/EditModal.tsx`

### 4. 已经正确实现的文件

以下文件已经正确处理了表单重置：

- `src/pages/Coupon/RightsCard/Type/EditModal/index.tsx`
- `src/pages/Coupon/Coupon/Type/EditModal.tsx`
- `src/pages/Appointment/RefundAuditModal.tsx`

### 5. 测试方法

修复后，请按以下步骤测试：

1. 打开列表页面
2. 点击编辑第一条记录，查看表单数据
3. 关闭模态框
4. 点击编辑第二条记录
5. 确认表单显示的是第二条记录的数据，而不是第一条

## 示例：活动管理修复

已修复的活动管理EditModal.tsx示例：

```typescript
// 当模态框打开时重置表单和状态
useEffect(() => {
  if (open) {
    if (info) {
      // 编辑模式：设置表单值和状态
      setContentType(info.contentType || 'content');
      form.setFieldsValue(info);
    } else {
      // 新增模式：重置表单和状态
      setContentType('content');
      form.resetFields();
      form.setFieldsValue({
        target: '用户端',
        contentType: 'content',
      });
    }
  }
}, [open, info, form]);
```

## 注意事项

1. 确保在`useEffect`的依赖数组中包含`open`、`info`和`form`
2. 如果组件有复杂的状态管理，需要在useEffect中同步更新所有相关状态
3. 对于有默认值的字段，在新增模式下要明确设置
4. 测试时要确保编辑不同记录时数据正确切换
